<table border="1" class="wikitable sortable">
<tr>
<th>Name</th>
<th>Type</th>
<th>Technologies</th>
<th>Author</th>
<th>Presented /
<p>Last updated</p>
</th>
<th>Licence*</th>
<th>Homepage</th>
</tr>
<tr>
<td>ABySS</td>
<td>(large) genomes</td>
<td>Solexa, SOLiD</td>
<td><PERSON>, J. et al.</td>
<td>2008 / 2011</td>
<td>NC-A</td>
<td><a class="external text" href="http://www.bcgsc.ca/platform/bioinfo/software/abyss" rel="nofollow">link</a></td>
</tr>
<tr>
<td>ALLPATHS-LG</td>
<td>(large) genomes</td>
<td>Solexa, SOLiD</td>
<td>Gnerre, S. et al.</td>
<td>2011</td>
<td>OS</td>
<td><a class="external text" href="http://www.broadinstitute.org/science/programs/genome-biology/crd" rel="nofollow">link</a></td>
</tr>
<tr>
<td>AMOS</td>
<td>genomes</td>
<td>Sanger, 454</td>
<td>Salzberg, S. et al.</td>
<td>2002? / 2008?</td>
<td>OS</td>
<td><a class="external text" href="http://sourceforge.net/apps/mediawiki/amos/index.php?title=AMOS" rel="nofollow">link</a></td>
</tr>
<tr>
<td>Arapan-M</td>
<td>Medium Genomes (e.g. E.coli)</td>
<td>All</td>
<td>Sahli, M. &amp; Shibuya, T.</td>
<td>2011 / 2012</td>
<td>OS</td>
<td><a class="external text" href="http://sourceforge.net/projects/dnascissor/files" rel="nofollow">link</a></td>
</tr>
<tr>
<td>Arapan-S</td>
<td>Small Genomes (Viruses and Bacteria)</td>
<td>All</td>
<td>Sahli, M. &amp; Shibuya, T.</td>
<td>2011 / 2012</td>
<td>OS</td>
<td><a class="external text" href="http://sourceforge.net/projects/dnascissor/files" rel="nofollow">link</a></td>
</tr>
<tr>
<td><a class="mw-redirect" href="//en.wikipedia.org/wiki/Celera" title="Celera">Celera</a> WGA Assembler / CABOG</td>
<td>(large) genomes</td>
<td>Sanger, 454, Solexa</td>
<td>Myers, G. et al.; Miller G. et al.</td>
<td>2004 / 2010</td>
<td>OS</td>
<td><a class="external text" href="http://www.jcvi.org/cms/research/projects/celera-assembler/overview/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>CLC Genomics Workbench &amp; CLC Assembly Cell</td>
<td>genomes</td>
<td>Sanger, 454, Solexa, SOLiD</td>
<td><a href="//en.wikipedia.org/wiki/CLC_bio" title="CLC bio">CLC bio</a></td>
<td>2008 / 2010 / 2011</td>
<td>C</td>
<td><a class="external text" href="http://www.clcbio.com/products/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>Cortex</td>
<td>genomes</td>
<td>Solexa, SOLiD</td>
<td>Iqbal, Z. <i>et al.</i></td>
<td>2011</td>
<td>OS</td>
<td><a class="external text" href="http://cortexassembler.sourceforge.net/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>DNA Baser</td>
<td>genomes</td>
<td>Sanger, 454</td>
<td>Heracle BioSoft SRL</td>
<td>01.2014</td>
<td>C</td>
<td>www.DnaBaser.com</td>
</tr>
<tr>
<td>DNA Dragon</td>
<td>genomes</td>
<td>Illumina, SOLiD, Complete Genomics, 454, Sanger</td>
<td>SequentiX</td>
<td>2011</td>
<td>C</td>
<td><a class="external text" href="https://www.dna-dragon.com/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>DNAnexus</td>
<td>genomes</td>
<td>Illumina, SOLiD, Complete Genomics</td>
<td>DNAnexus</td>
<td>2011</td>
<td>C</td>
<td><a class="external text" href="https://dnanexus.com/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>Edena</td>
<td>genomes</td>
<td>Illumina</td>
<td>D. Hernandez, P. François, L. Farinelli, M. Osteras, and J. Schrenzel.</td>
<td>2008/2013</td>
<td>OS</td>
<td><a class="external text" href="http://www.genomic.ch/edena.php" rel="nofollow">link</a></td>
</tr>
<tr>
<td>Euler</td>
<td>genomes</td>
<td>Sanger, 454 (,Solexa ?)</td>
<td>Pevzner, P. et al.</td>
<td>2001 / 2006?</td>
<td>(C / NC-A?)</td>
<td><a class="external text" href="http://nbcr.sdsc.edu/euler/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>Euler-sr</td>
<td>genomes</td>
<td>454, Solexa</td>
<td>Chaisson, MJ. et al.</td>
<td>2008</td>
<td>NC-A</td>
<td><a class="external text" href="http://euler-assembler.ucsd.edu/portal/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>Forge</td>
<td>(large) genomes, EST, metagenomes</td>
<td>454, Solexa, SOLID, Sanger</td>
<td>Platt, DM, Evers, D.</td>
<td>2010</td>
<td>OS</td>
<td><a class="external text" href="http://combiol.org/forge/" rel="nofollow">link</a></td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Geneious" title="Geneious">Geneious</a></td>
<td>genomes</td>
<td>Sanger, 454, Solexa, Ion Torrent, Complete Genomics, PacBio, Oxford Nanopore, Illumina</td>
<td><a href="//en.wikipedia.org/wiki/Biomatters" title="Biomatters">Biomatters Ltd</a></td>
<td>2009 / 2013</td>
<td>C</td>
<td><a class="external text" href="http://geneious.com/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>Graph Constructor</td>
<td>(large) genomes</td>
<td>Sanger, 454, Solexa, SOLiD</td>
<td><a class="new" href="//en.wikipedia.org/w/index.php?title=Convey_Computer&amp;action=edit&amp;redlink=1" title="Convey Computer (page does not exist)">Convey Computer Corporation</a></td>
<td>2011</td>
<td>C</td>
<td><a class="external text" href="http://www.conveycomputer.com/lifesciences/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>IDBA (Iterative De Bruijn graph short read Assembler)</td>
<td>(large) genomes</td>
<td>Sanger,454,Solexa</td>
<td>Yu Peng, Henry C. M. Leung, Siu-Ming Yiu, <a class="mw-redirect" href="//en.wikipedia.org/wiki/Y._L._Chin" title="Y. L. Chin">Francis Y. L. Chin</a></td>
<td>2010</td>
<td>(C / NC-A?)</td>
<td><a class="external text" href="http://www.cs.hku.hk/~alse/idba/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>LIGR Assembler (derived from TIGR Assembler)</td>
<td>genomic</td>
<td>Sanger</td>
<td>-</td>
<td>2009/ 2012</td>
<td>OS</td>
<td><a class="external text" href="http://sourceforge.net/projects/ligr-assembler/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>MaSuRCA (Maryland Super Read - Celera Assembler)</td>
<td>(large) genomes</td>
<td>Sanger, Illumina, 454</td>
<td>Aleksey Zimin, Guillaume Marçais, Daniela Puiu, Michael Roberts, Steven L. Salzberg, James A. Yorke</td>
<td>2012 / 2013</td>
<td>OS</td>
<td><a class="external text" href="http://www.genome.umd.edu/masurca.html" rel="nofollow">link</a></td>
</tr>
<tr>
<td>MIRA (Mimicking Intelligent Read Assembly)</td>
<td>genomes, ESTs</td>
<td>Sanger, 454, Solexa</td>
<td>Chevreux, B.</td>
<td>1998 / 2011</td>
<td>OS</td>
<td><a class="external text" href="http://sourceforge.net/apps/mediawiki/mira-assembler/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>NextGENe</td>
<td>(small genomes?)</td>
<td>454, Solexa, SOLiD</td>
<td>Softgenetics</td>
<td>2008</td>
<td>C</td>
<td><a class="external text" href="http://softgenetics.com/NextGENe.html" rel="nofollow">link</a></td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Newbler" title="Newbler">Newbler</a></td>
<td>genomes, ESTs</td>
<td>454, Sanger</td>
<td>454/Roche</td>
<td>2009</td>
<td>C</td>
<td><a class="external text" href="http://www.454.com/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>PADENA</td>
<td>genomes</td>
<td>454, Sanger</td>
<td>454/Roche</td>
<td>2010</td>
<td>OS</td>
<td><a class="external text" href="http://bio.codeplex.com/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>PASHA</td>
<td>(large) genomes</td>
<td>Illumina</td>
<td>Liu, Schmidt, Maskell</td>
<td>2011</td>
<td>OS</td>
<td><a class="external text" href="http://sites.google.com/site/yongchaosoftware/pasha" rel="nofollow">link</a></td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Phrap" title="Phrap">Phrap</a></td>
<td>genomes</td>
<td>Sanger, 454, Solexa</td>
<td>Green, P.</td>
<td>1994 / 2008</td>
<td>C / NC-A</td>
<td><a class="external text" href="http://www.phrap.org/" rel="nofollow">link</a></td>
</tr>
<tr>
<td><a class="mw-redirect" href="//en.wikipedia.org/wiki/The_Institute_for_Genomic_Research" title="The Institute for Genomic Research">TIGR</a> Assembler</td>
<td>genomic</td>
<td>Sanger</td>
<td>-</td>
<td>1995 / 2003</td>
<td>OS</td>
<td><a class="external text" href="ftp://ftp.jcvi.org/pub/software/assembler/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>Ray<sup class="reference" id="cite_ref-7"><a href="#cite_note-7"><span>[</span>7<span>]</span></a></sup></td>
<td>genomes</td>
<td>Illumina, mix of Illumina and 454, paired or not</td>
<td>Sébastien Boisvert, François Laviolette &amp; Jacques Corbeil.</td>
<td>2010</td>
<td>OS [GNU General Public License]</td>
<td><a class="external text" href="http://denovoassembler.sf.net/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>Sequencher</td>
<td>genomes</td>
<td>traditional and next generation sequence data</td>
<td><a href="//en.wikipedia.org/wiki/Gene_Codes_Corporation" title="Gene Codes Corporation">Gene Codes Corporation</a></td>
<td>1991 / 2009 / 2011</td>
<td>C</td>
<td><a class="external text" href="http://www.genecodes.com/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>SeqMan NGen</td>
<td>(large) genomes, exomes, transcriptomes, metagenomes, ESTs</td>
<td>Illumina, ABI SOLiD, Roche 454, Ion Torrent, Solexa, Sanger</td>
<td><a href="//en.wikipedia.org/wiki/DNASTAR" title="DNASTAR">DNASTAR</a></td>
<td>2007 / 2011</td>
<td>C</td>
<td><a class="external text" href="http://www.dnastar.com/t-products-seqman-ngen.aspx" rel="nofollow">link</a></td>
</tr>
<tr>
<td>SGA</td>
<td>(large) genomes</td>
<td>Illumina, Sanger (Roche 454?, Ion Torrent?)</td>
<td>Simpson, J.T. et al.</td>
<td>2011 / 2012</td>
<td>OS</td>
<td><a class="external text" href="https://github.com/jts/sga" rel="nofollow">link</a></td>
</tr>
<tr>
<td>SHARCGS</td>
<td>(small) genomes</td>
<td>Solexa</td>
<td>Dohm et al.</td>
<td>2007 / 2007</td>
<td>OS</td>
<td><a class="external text" href="http://sharcgs.molgen.mpg.de/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>SOPRA</td>
<td>genomes</td>
<td>Illumina, SOLiD, Sanger, 454</td>
<td>Dayarian, A. et al.</td>
<td>2010 / 2011</td>
<td>OS</td>
<td><a class="external text" href="http://www.physics.rutgers.edu/~anirvans/SOPRA/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>SparseAssembler</td>
<td>(large) genomes</td>
<td>Illumina, 454, Ion torrent</td>
<td>Ye, C. et al.</td>
<td>2012 / 2012</td>
<td>OS</td>
<td><a class="external text" href="https://sites.google.com/site/sparseassembler/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>SSAKE</td>
<td>(small) genomes</td>
<td>Solexa (SOLiD? Helicos?)</td>
<td>Warren, R. et al.</td>
<td>2007 / 2007</td>
<td>OS</td>
<td><a class="external text" href="http://www.bcgsc.ca/platform/bioinfo/software/ssake" rel="nofollow">link</a></td>
</tr>
<tr>
<td>SOAPdenovo</td>
<td>genomes</td>
<td>Solexa</td>
<td>Li, R. et al.</td>
<td>2009 / 2009</td>
<td>OS</td>
<td><a class="external text" href="http://soap.genomics.org.cn/soapdenovo.html" rel="nofollow">link</a></td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/SPAdes_(software)" title="SPAdes (software)">SPAdes</a></td>
<td>(small) genomes, single-cell</td>
<td>Illumina, Solexa</td>
<td>Bankevich, A et al.</td>
<td>2012 / 2013</td>
<td>OS</td>
<td><a class="external text" href="http://bioinf.spbau.ru/en/spades" rel="nofollow">link</a></td>
</tr>
<tr>
<td>Staden gap4 package</td>
<td>BACs (, small genomes?)</td>
<td>Sanger</td>
<td>Staden et al.</td>
<td>1991 / 2008</td>
<td>OS</td>
<td><a class="external text" href="http://staden.sourceforge.net/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>Taipan</td>
<td>(small) genomes</td>
<td>Illumina</td>
<td>Schmidt, B. <i>et al.</i></td>
<td>2009</td>
<td>OS</td>
<td><a class="external text" href="http://sourceforge.net/projects/taipan/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>VCAKE</td>
<td>(small) genomes</td>
<td>Solexa (SOLiD?, Helicos?)</td>
<td>Jeck, W. et al.</td>
<td>2007 / 2007</td>
<td>OS</td>
<td><a class="external text" href="http://sourceforge.net/projects/vcake" rel="nofollow">link</a></td>
</tr>
<tr>
<td>Phusion assembler</td>
<td>(large) genomes</td>
<td>Sanger</td>
<td>Mullikin JC, <i>et al.</i></td>
<td>2003</td>
<td>OS</td>
<td><a class="external text" href="http://www.sanger.ac.uk/Software/production/phusion/" rel="nofollow">link</a></td>
</tr>
<tr>
<td>Quality Value Guided SRA (QSRA)</td>
<td>genomes</td>
<td>Sanger, Solexa</td>
<td>Bryant DW, <i>et al.</i></td>
<td>2009</td>
<td>OS</td>
<td><a class="external text" href="http://qsra.cgrb.oregonstate.edu/" rel="nofollow">link</a></td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Velvet_assembler" title="Velvet assembler">Velvet</a></td>
<td>(small) genomes</td>
<td>Sanger, 454, Solexa, SOLiD</td>
<td>Zerbino, D. et al.</td>
<td>2007 / 2009</td>
<td>OS</td>
<td><a class="external text" href="http://www.ebi.ac.uk/~zerbino/velvet/" rel="nofollow">link</a></td>
</tr>
<tr>
<td colspan="7" style="border-top: 1px solid #333;"><small>*<b>Licences:</b> OS = Open Source; C = Commercial; C / NC-A = Commercial but free for non-commercial and academics; Brackets = unclear, but most likely C / NC-A</small></td>
</tr>
</table>
