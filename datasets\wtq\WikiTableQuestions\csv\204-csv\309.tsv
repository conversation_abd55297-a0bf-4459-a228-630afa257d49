Model	Released	Usage	Features	Storage
Betsie/Dottie	Unknown	Used for calculating odds for bookmakers		
SADIE	1966	Sterling And Decimal Invoicing Electronically	Programming stored on soldered-through connectors on double-sided printed circuit cards, connecting instruction type on one side of the card with instruction number on the other side. IBM 2741 Selectric-style golf-ball teleprinter for user interface.	Nickel acoustic delay line working storage (8 words of decimal arithmetic). Paper tape and edge-punched cards. Magnetic stripe cards for the teleprinter-style user interface. Could be switched between decimal and Sterling at the flick of a switch as it said in the publicity.
SUSIE	1967	Stock Updating and Sales Invoicing Electronically	Programmes (2) stored on drum, each with 1000 machine instructions. Programme could be loaded from or written out to paper tape. User interface as SADIE.	As for SADIE, plus magnetic drum (6k words)
Mark 1	1970	Sales Order processing	Architecture similar to the Data General "NOVA" in that the memory and I/O highways were separate whereas DEC used a common highway for memory and I/O with the peripherals occupying memory addresses. The NOVA was a 16 bit design but the Molecular was 18 bit allowing signed arithmetic and a memory parity check bit. The instruction set was in some ways similar to the Nova. The M18 was designed by a small team headed by <PERSON> & <PERSON>.\nOperating system known as LOS developed by <PERSON> in Leicester office of Systemation. Programming was done in octal notation machine code in longhand using coding sheets. The 17 bit word allowed the limited storage of 3 bytes per word using "Metacode"	Ferrite core memory. from Plessey or Fabritek and later EMM.\nFront-loading disk drive\nOptional paper tape reader D400 front-loading cartridge 875 kbit D800 1.75 Mbit
Mark 2	1973	Sales Order Processing	Totally new processor with hardware interrupt stacking, a variable cycle time and new disc controller. Same basic architecture as the MK1. Used a totally new OS.\nWater-cooled cabinets with remote chiller unit initially, later normal fan cooling.	Fabritek or EMM ferrite core memory. Introduced the DD1600 CDC 9427 top-loading disk drive one fixed one removable and/or a large multi-platter CDC removable disk drive.
Mark 3			Designation not used but was effectively the 3ME/6ME which was a MK2 processor modified to run the MK1 OS and application software. Hardware interrupt stacking disabled. Used a low capacity version of the CDC 9427	
Mark 4		SOP, Livestock Markets, Paper merchants, Plumbers merchants.	Developed after the demise of BCL by ABS computers. Basically a 6ME in a new cabinet with lead acid battery backed static RAM instead of ferrite core.\nProgramming still done in octal notation machine code in longhand using coding sheets.	Core Memory or static RAM, Introduced the CDC Hawk 9427H disk drive, up to 4 supported per controller, max 2 controllers and/or a large multi-platter CDC D8000 removable disk drive.
Mark 5	1984	SOP, Livestock Markets, Paper merchants, Plumbers merchants.	Basically a re engineering of the 6ME processor to remove redundant stack logic and reduced to 3 boards with a printed circuit backplane instead of the costly wire wrapped backplane used for earlier models.\nHardware re-design by Systemation Developments K. A. Howlett (Keith Alec b1943 son of W A Howlett)with assistance in the final test stages by G. Boote. Cabinet design by Business Computers Systems Ltd. Hardware designed and manufactured by Systemation Developments for Business computers Systems.	Initially large-format Ampex ferrite core memory then static RAM, both introduced by Systemation Developments.\nLater Bank switching memory introduced (32K 18 bit words base memory plus up to 8 X 32K banks).\nFirst sold with CDC Hawk 9427H drives later CDC Lark 2 disk drives. Memory and new RS232 4 port I/O card (Quad I/O) by K. A. Howlett, Lark 2 disc controller by J. Adams. Up to four CDC Lark or Amcodyne drives per controller, max 2 controllers.
Distributor	1986	SOP, Livestock Markets, Paper merchants, Plumbers merchants.	Smaller version of the Mark 5 in an oversized PC style vertical cabinet with a CDC Lark 2 drive built in. Designed and manufactured by Systemation Developments K. A. Howlett for Business Computers Systems. A single board processor was developed to replace the 3 card processor and was working but never came to market. At the same time a Transputer based maths co-processor had also been developed. Assembler programming introduced by Systemation Developments with the aid of a third party, running on PC's with the program then downloaded to the M18.	Support for seven additional external Lark 2 or Amcodyne drives 4 per controller including the integral drive. 32K 18 bit words of base memory plus up to 8 32K banks.
	1990(?)	SOP, Livestock Markets, Paper merchants, Plumbers merchants.	A single board processor was developed to replace the 3 card processor and was working but never came to market. At the same time a Transputer based maths co-processor had also been developed. Assembler programming introduced by Systemation Developments with the aid of a third party, running on PC's with the program then downloaded to the M18.	Support for seven additional external Lark 2 or Amcodyne drives 4 per controller including the integral drive. 32K 18 bit words of base memory plus up to 8 32K banks.
Distributor EP	1989	SOP, Livestock Markets	Enhanced version of the Distributor	SCSI disk and tape support
