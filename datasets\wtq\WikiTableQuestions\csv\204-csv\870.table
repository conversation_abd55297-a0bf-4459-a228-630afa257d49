| Product                                                   | Main Functionality                                                                                                      | Input Format                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | Output Format                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | Platform                                  | License and cost                                      | Notes                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| AllegroGraph                                              | Graph Database. RDF with Gruff visualization tool                                                                       | RDF                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | RDF                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | Linux, Mac, Windows                       | Free and Commercial                                   | AllegroGraph is a graph database. It is disk-based, fully transactional OLTP database that stores data structured in graphs rather than in tables. AllegroGraph includes a Social Networking Analytics library.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| EgoNet                                                    | Ego-centric network analysis                                                                                            | Conducts interviews or takes any valid XML file                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | Output to CSV and convertible to almost any other format                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | Any system supporting Java                | Open Source, seeking contributors                     | Egonet is a program for the collection and analysis of egocentric network data. Egonet contains facilities to assist in creating the questionnaire, collecting the data and providing general global network measures and data matrixes that can be used in further analysis by other software programs.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| Gephi                                                     | Graph exploration and manipulation software                                                                             | GraphViz(.dot), Graphlet(.gml), GUESS(.gdf), LEDA(.gml), NetworkX(.graphml, .net), NodeXL(.graphml, .net), <PERSON>jek(.net, .gml), Sonivis(.graphml), <PERSON><PERSON>(.tlp, .dot), UCINET(.dl), yEd(.gml), <PERSON><PERSON><PERSON> (.gexf), Edge list(.csv), databases                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | GUESS(.gdf), Gephi(.gexf), .svg, .png                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | Any system supporting Java 1.6 and OpenGL | Open Source (GPL3), seeking contributors              | Gephiis an interactive visualization and exploration platform for all kinds of networks and complex systems, dynamic and hierarchical graphs. It is a tool for people that have to explore and understand graphs. The user interacts with the representation, manipulate the structures, shapes and colors to reveal hidden properties. It uses a 3D render engine to display large networks in real-time and to speed up the exploration. A flexible and multi-task architecture brings new possibilities to work with complex data sets and produce valuable visual results.                                                                                                                                                                                                                                                                                                                                                                      |
| GraphStream                                               | Dynamic Graph Library                                                                                                   | GraphStream(.dgs), GraphViz(.dot), Graphlet(.gml), edge list                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | GraphStream(.dgs), GraphViz(.dot), Graphlet(.gml), image sequence                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | Any system supporting Java                | Open Source                                           | With GraphStream you deal with graphs. Static and Dynamic. You create them from scratch, from a file or any source. You display and render them.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| Graph-tool                                                | Python module for efficient analysis and visualization of graphs.                                                       | GraphViz(.dot), GraphML                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | GraphViz(.dot), GraphML, .bmp, .canon, .cmap, .eps, .fig, .gd, .gd2, .gif, .gtk, .ico, .imap, .cmapx, .ismap, .jpeg, .pdf, .plain, .png, .ps, .ps2, .svg, .svgz, .tif, .vml, .vmlz, .vrml, .wbmp, .xlib                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | GNU/Linux, Mac                            | Free Software (GPL3)                                  | Graph-tool is a python module for efficient analysis of graphs. Its core data structures and algorithms are implemented in C++, with heavy use of Template metaprogramming, based on the Boost Graph Library. It contains a comprehensive list of algorithms.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| Graphviz                                                  | Graph vizualisation software                                                                                            | GraphViz(.dot)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | .bmp, .canon, .cmap, .eps, .fig, .gd, .gd2, .gif, .gtk, .ico, .imap, .cmapx, .ismap, .jpeg, .pdf, .plain, .png, .ps, .ps2, .svg, .svgz, .tif, .vml, .vmlz, .vrml, .wbmp, .xlib                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | Linux, Mac, Windows                       | Open Source (CPL)                                     | Graphviz is open source graph visualization framework. It has several main graph layout programs suitable for social network visualization.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| Java Universal Network/Graph (JUNG) Framework             | network and graph manipulation, analysis, and visualization                                                             | built-in support for GraphML, Pajek, and some text formats; user can create parsers for any desired format                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | built-in support for GraphML, Pajek, and some text formats; user can create exporters for any desired format                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | Any platform supporting Java              | Open source (BSD license)                             | JUNG is a Java API and library that provides a common and extensible language for the modeling, analysis, and visualization of relational data. It supports a variety of graph types (including hypergraphs), supports graph elements of any type and with any properties, enables customizable visualizations, and includes algorithms from graph theory, data mining, and social network analysis (e.g., clustering, decomposition, optimization, random graph generation, statistical analysis, distances, flows, and centrality (PageRank, HITS, etc.)). It is limited only by the amount of memory allocated to Java.                                                                                                                                                                                                                                                                                                                          |
| Mathematica                                               | Graph analysis, statistics, data visualization, optimization, image recognition.                                        | 3DS, ACO, Affymetrix, AIFF, ApacheLog, ArcGRID, AU, AVI, Base64, BDF, Binary, Bit, BMP, Byte, BYU, BZIP2, CDED, CDF, Character16, Character8, CIF, Complex128, Complex256, Complex64, CSV, CUR, DBF, DICOM, DIF, DIMACS, Directory, DOT, DXF, EDF, EPS, ExpressionML, FASTA, FITS, FLAC, GenBank, GeoTIFF, GIF, GPX, Graph6, Graphlet, GraphML, GRIB, GTOPO30, GXL, GZIP, HarwellBoeing, HDF, HDF5, HTML, ICO, ICS, Integer128, Integer16, Integer24, Integer32, Integer64, Integer8, JPEG, JPEG2000, JSON, JVX, KML, LaTeX, LEDA, List, LWO, MAT, MathML, MBOX, MDB, MGF, MMCIF, MOL, MOL2, MPS, MTP, MTX, MX, NASACDF, NB, NDK, NetCDF, NEXUS, NOFF, OBJ, ODS, OFF, Package, Pajek, PBM, PCX, PDB, PDF, PGM, PLY, PNG, PNM, PPM, PXR, QuickTime, RawBitmap, Real128, Real32, Real64, RIB, RSS, RTF, SCT, SDF, SDTS, SDTSDEM, SHP, SMILES, SND, SP3, Sparse6, STL, String, SurferGrid, SXC, Table, TAR, TerminatedString, Text, TGA, TGF, TIFF, TIGER, TLE, TSV, USGSDEM, UUE, VCF, VCS, VTK, WAV, Wave64, WDX, XBM, XHTML, XHTMLMathML, XLS, XLSX, XML, XPORT, XYZ, ZIP | 3DS, ACO, AIFF, AU, AVI, Base64, Binary, Bit, BMP, Byte, BYU, BZIP2, C, CDF, Character16, Character8, Complex128, Complex256, Complex64, CSV, DICOM, DIF, DIMACS, DOT, DXF, EMF, EPS, ExpressionML, FASTA, FITS, FLAC, FLV, GIF, Graph6, Graphlet, GraphML, GXL, GZIP, HarwellBoeing, HDF, HDF5, HTML, Integer128, Integer16, Integer24, Integer32, Integer64, Integer8, JPEG, JPEG2000, JSON, JVX, KML, LEDA, List, LWO, MAT, MathML, Maya, MGF, MIDI, MOL, MOL2, MTX, MX, NASACDF, NB, NetCDF, NEXUS, NOFF, OBJ, OFF, Package, Pajek, PBM, PCX, PDB, PDF, PGM, PLY, PNG, PNM, POV, PPM, PXR, QuickTime, RawBitmap, Real128, Real32, Real64, RIB, RTF, SCT, SDF, SND, Sparse6, STL, String, SurferGrid, SVG, SWF, Table, TAR, TerminatedString, TeX, Text, TGA, TGF, TIFF, TSV, UUE, VideoFrames, VRML, VTK, WAV, Wave64, WDX, WMF, X3D, XBM, XHTML, XHTMLMathML, XLS, XLSX, XML, XYZ, ZIP, ZPR | Windows, Macintosh, Linux                 | Commercial                                            | Mathematica is a general purpose computation and analysis environment.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| Netlytic                                                  | Cloud based text & social network analyzer                                                                              | RSS, Google Drive, Twitter, YouTube comments, .csv, .txt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | .csv, .mds, .dl (UCINET), .net (Pajek)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | Windows, Linux, Mac                       | Freemium                                              | Netlytic allows users to automatically summarize large volumes of text & discover social networks from conversations on social media such as Twitter, YouTube, blogs, online forums & chats. Netlytic can automatically build chain networks & personal name networks, based on who replies to whom & who mentioned whom.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| Network Overview Discovery Exploration for Excel (NodeXL) | Network overview, discovery and exploration                                                                             | email, .csv (text), .txt, .xls (Excel), .xslt (Excel 2007, 2010, 2013), .net (Pajek), .dl (UCINet), GraphML                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               | .csv (text), .txt, .xls (Excel), .xslt (Excel 2007), .dl (UCINet), GraphML                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | Windows XP/Vista/7                        | Free (Ms-PL)                                          | NodeXL is a free and open Excel 2007, 2010, 2013 Add-in and C#/.Net library for network analysis and visualization. It integrates into Excel 2007, 2010, 2013 and adds directed graph as a chart type to the spreadsheet and calculates a core set of network metrics and scores. Supports extracting email, Twitter, YouTube, Facebook, WWW, Wiki and flickr social networks. Accepts edge lists and matrix representations of graphs. Allows for easy and automated manipulation and filtering of underlying data in spreadsheet format. Multiple network visualization layouts. Reads and writes Pajek, UCINet and GraphML files.                                                                                                                                                                                                                                                                                                                |
| NetMiner 4                                                | All-in-one Software for Network Analysis and Visualization                                                              | .xls(Excel),.xlsx (Excel 2007), .csv(text), .dl(UCINET), .net(Pajek), .dat(StOCNET), .gml; NMF(proprietary)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               | .xls(Excel),.xlsx (Excel 2007), .csv(text), .dl(UCINET), .net(Pajek), .dat(StOCNET), NMF(proprietary)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | Microsoft Windows                         | Commercial with free trial                            | NetMiner is a software tool for exploratory analysis and visualization of large network data. NetMiner 4 embed internal Python-based script engine which equipped with the automatic Script Generator for unskilled users. Then the users can operate NetMiner 4 with existing GUI or programmable script language.
Main features include : analysis of large networks(+10,000,000 nodes), comprehensive network measures and models, both exploratory & confirmatory analysis, interactive visual analytics, what-if network analysis, built-in statistical procedures and charts, full documentation(1,000+ pages of User's Manual), expressive network data model, facilities for data & workflow management, Python-based Script workbench and user-friendliness.                                                                                                                                                                               |
| NetworkX                                                  | Python package for the creation, manipulation, and study of the structure, dynamics, and functions of complex networks. | GML, Graph6/Sparse6, GraphML, GraphViz (.dot), NetworkX (.yaml, adjacency lists, and edge lists), Pajek (.net), LEDA                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      | GML, Gnome Dia, Graph6/Sparse6, GraphML, GraphViz (.dot), NetworkX (.yaml, adjacency lists, and edge lists), Pajek (.net), LEDA, and assorted image formats (.jpg, .png, .ps, .svg, et al.)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      | Open source (GPL and similar)             | Free                                                  | NetworkX (NX) is a toolset for graph creation, manipulation, analysis, and visualization. User interface is through scripting/command-line provided by Python. NX includes a several algorithms, metrics and graph generators. Visualization is provided through pylab and graphviz.
NX is an open-source project, in active development since 2004 with an open bug-tracking site, and user forums. Development is sponsored by Los Alamos National Lab.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| R                                                         | Social network analysis within the versatile and popular R environment                                                  | R will read in almost any format data file                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | R has write capability for most data formats                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | Windows, Linux, Mac                       | Open source                                           | R contains several packages relevant for social network analysis: igraph is a generic network analysis package; sna performs sociometric analysis of networks; network manipulates and displays network objects; tnet performs analysis of weighted networks, two-mode networks, and longitudinal networks; ergm is a set of tools to analyze and simulate networks based on exponential random graph models exponential random graph models; Bergm provides tools for Bayesian analysis for exponential random graph models, hergm implements hierarchical exponential random graph models; 'RSiena' allows the analyses of the evolution of social networks using dynamic actor-oriented models; latentnet has functions for network latent position and cluster models; degreenet provides tools for statistical modeling of network degree distributions; and networksis provides tools for simulating bipartite networks with fixed marginals. |
| SVAT                                                      | Visual analytics for investigation                                                                                      | GraphViz(.dot), Graphlet(.gml), GUESS(.gdf), LEDA(.gml), NetworkX(.graphml, .net), NodeXL(.graphml, .net), Pajek(.net, .gml), Sonivis(.graphml), Tulip(.tlp, .dot), UCINET(.dl), yEd(.gml), Gephi (.gexf), Edge list(.csv), databases - Oracle, MSSQL, PostgreSQL, MySQL, Webservices...                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | GUESS(.gdf), Gephi(.gexf), .svg, .png                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | Any system supporting Java 1.6 and OpenGL | Closed source modules, Open Source modules from Gephi | Commercial tool based on Gephi.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| Tulip                                                     | Social Network Analysis tool                                                                                            | Tulip format (.tlp), GraphViz (.dot), GML, txt, adjacency matrix                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | .tlp, .gml                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | Windows Vista, XP, 7/ Linux / Mac OS      | LGPL                                                  | Tulip is an information visualization framework dedicated to the analysis and visualization of relational data. Tulip aims to provide the developer with a complete library, supporting the design of interactive information visualization applications for relational data that can be tailored to the problems he or she is addressing.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| visone                                                    | Visual Social Network Analyses and Exploration                                                                          | many formats                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | many formats                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | Windows, Linux, Mac OS (Java based)       | Free (also for commercial use)                        | visone is a software for the analysis and visualization of social networks. It is currently developed by Algorithmics group at the University of Konstanz.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| Wolfram Alpha                                             | Graph analysis, time series analysis, categorical data analysis                                                         | Facebook API                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | Many formats                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | Web service                               | Free                                                  | Wolfram Alpha is a general computational knowledge engine answering queries on many knowledge domains. Give it the input "Facebook report" and it will answer queries on analysis of your social network data,                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
