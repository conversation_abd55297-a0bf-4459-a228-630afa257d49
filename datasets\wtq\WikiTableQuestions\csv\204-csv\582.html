<table class="wikitable" style="font-size: 85%; text-align: center">
<tr>
<th rowspan="2">Model</th>
<th rowspan="2">Launch</th>
<th rowspan="2"><a href="//en.wikipedia.org/wiki/Code_name" title="Code name">Code name</a></th>
<th rowspan="2">Fab (<a class="mw-redirect" href="//en.wikipedia.org/wiki/Nanometer" title="Nanometer">nm</a>)</th>
<th rowspan="2"><a class="mw-redirect" href="//en.wikipedia.org/wiki/Computer_bus" title="Computer bus">Bus</a> <a class="mw-redirect" href="//en.wikipedia.org/wiki/I/O_interface" title="I/O interface">interface</a></th>
<th rowspan="2">Memory (<a href="//en.wikipedia.org/wiki/Mebibyte" title="Mebibyte">MiB</a>)</th>
<th rowspan="2">Core clock (<a href="//en.wikipedia.org/wiki/Hertz" title="Hertz">MHz</a>)</th>
<th rowspan="2">Memory clock (<a href="//en.wikipedia.org/wiki/Hertz" title="Hertz">MHz</a>)</th>
<th rowspan="2">Config core<sup>1</sup></th>
<th colspan="4"><a href="//en.wikipedia.org/wiki/Fillrate" title="Fillrate">Fillrate</a></th>
<th colspan="3">Memory</th>
<th rowspan="2"><a href="//en.wikipedia.org/wiki/DirectX" title="DirectX">DirectX</a> support</th>
</tr>
<tr>
<th>MOperations/s</th>
<th>MPixels/s</th>
<th>MTextels/s</th>
<th>MVertices/s</th>
<th>Bandwidth (<a href="//en.wikipedia.org/wiki/Gigabyte" title="Gigabyte">GB</a>/s)</th>
<th>Bus type</th>
<th>Bus width (<a href="//en.wikipedia.org/wiki/Bit" title="Bit">bit</a>)</th>
</tr>
<tr>
<th style="text-align:left">Voodoo Graphics</th>
<td>October 1, 1996</td>
<td>SST1</td>
<td>500</td>
<td>PCI</td>
<td>2, 4</td>
<td>50</td>
<td>50</td>
<td>1:0:1:1</td>
<td>50</td>
<td>50</td>
<td>50</td>
<td>0</td>
<td>0.8</td>
<td>EDO</td>
<td>128</td>
<td>3.0</td>
</tr>
<tr>
<th style="text-align:left">Voodoo Rush</th>
<td>April 1997</td>
<td>SST96</td>
<td>500</td>
<td>AGP 2x, PCI</td>
<td>2, 4</td>
<td>50</td>
<td>50</td>
<td>1:0:1:1</td>
<td>50</td>
<td>50</td>
<td>50</td>
<td>0</td>
<td>0.4</td>
<td>EDO</td>
<td>64</td>
<td>3.0</td>
</tr>
<tr>
<th style="text-align:left">Voodoo2</th>
<td>March 1, 1998</td>
<td>SST96</td>
<td>350</td>
<td>PCI</td>
<td>8, 12</td>
<td>90</td>
<td>90</td>
<td>1:0:2:1</td>
<td>90</td>
<td>90</td>
<td>180</td>
<td>0</td>
<td>0.72</td>
<td>EDO</td>
<td>64</td>
<td>3.0</td>
</tr>
<tr>
<th style="text-align:left">Voodoo Banshee</th>
<td>June 22, 1998</td>
<td>Banshee</td>
<td>350</td>
<td>AGP 2x, PCI</td>
<td>8, 16</td>
<td>100</td>
<td>100</td>
<td>1:0:1:1</td>
<td>100</td>
<td>100</td>
<td>100</td>
<td>0</td>
<td>1.6</td>
<td>SDR</td>
<td>128</td>
<td>6.0</td>
</tr>
<tr>
<th style="text-align:left">Velocity 100</th>
<td>July 26, 1999</td>
<td>Avenger</td>
<td>250</td>
<td>AGP 2x</td>
<td>8</td>
<td>143</td>
<td>143</td>
<td>1:0:2:1</td>
<td>143</td>
<td>143</td>
<td>286</td>
<td>0</td>
<td>2.288</td>
<td>SDR</td>
<td>128</td>
<td>6.0</td>
</tr>
<tr>
<th style="text-align:left">Velocity 200</th>
<td>July 26, 1999</td>
<td>Avenger</td>
<td>250</td>
<td>AGP 2x</td>
<td>12</td>
<td>143</td>
<td>143</td>
<td>1:0:2:1</td>
<td>143</td>
<td>143</td>
<td>286</td>
<td>0</td>
<td>2.288</td>
<td>SDR</td>
<td>128</td>
<td>6.0</td>
</tr>
<tr>
<th style="text-align:left">Voodoo3 1000</th>
<td>March 1999</td>
<td>Avenger</td>
<td>250</td>
<td>AGP 2x, PCI</td>
<td>8, 16</td>
<td>125</td>
<td>125</td>
<td>1:0:2:1</td>
<td>125</td>
<td>125</td>
<td>250</td>
<td>0</td>
<td>2</td>
<td>SDR</td>
<td>128</td>
<td>6.0</td>
</tr>
<tr>
<th style="text-align:left">Voodoo3 2000</th>
<td>April 3, 1999</td>
<td>Avenger</td>
<td>250</td>
<td>AGP 2x, PCI</td>
<td>16</td>
<td>143</td>
<td>143</td>
<td>1:0:2:1</td>
<td>143</td>
<td>143</td>
<td>286</td>
<td>0</td>
<td>2.288</td>
<td>SDR</td>
<td>128</td>
<td>6.0</td>
</tr>
<tr>
<th style="text-align:left">Voodoo3 3000</th>
<td>April 3, 1999</td>
<td>Avenger</td>
<td>250</td>
<td>AGP 2x, PCI</td>
<td>16</td>
<td>166</td>
<td>166</td>
<td>1:0:2:1</td>
<td>166</td>
<td>166</td>
<td>333</td>
<td>0</td>
<td>2.66</td>
<td>SDR</td>
<td>128</td>
<td>6.0</td>
</tr>
<tr>
<th style="text-align:left">Voodoo3 3500 TV</th>
<td>June 1999</td>
<td>Avenger</td>
<td>250</td>
<td>AGP 2x, PCI</td>
<td>16</td>
<td>183</td>
<td>183</td>
<td>1:0:2:1</td>
<td>183</td>
<td>183</td>
<td>366</td>
<td>0</td>
<td>2.928</td>
<td>SDR</td>
<td>128</td>
<td>6.0</td>
</tr>
<tr>
<th style="text-align:left">Voodoo4 4200</th>
<td>Never Released</td>
<td>VSA-100</td>
<td>250</td>
<td>AGP 4x, PCI</td>
<td>32</td>
<td>183</td>
<td>183</td>
<td>2:0:2:2</td>
<td>366</td>
<td>366</td>
<td>366</td>
<td>0</td>
<td>1.464</td>
<td>SDR</td>
<td>64</td>
<td>6.0</td>
</tr>
<tr>
<th style="text-align:left">Voodoo4 4500</th>
<td>October 13, 2000</td>
<td>VSA-100</td>
<td>250</td>
<td>AGP 4x, PCI</td>
<td>32</td>
<td>166</td>
<td>166</td>
<td>2:0:2:2</td>
<td>332</td>
<td>332</td>
<td>332</td>
<td>0</td>
<td>2.656</td>
<td>SDR</td>
<td>128</td>
<td>6.0</td>
</tr>
<tr>
<th style="text-align:left">Voodoo4 4800</th>
<td>Never Released</td>
<td>VSA-100</td>
<td>250</td>
<td>AGP 4x, PCI</td>
<td>32</td>
<td>200</td>
<td>200</td>
<td>2:0:2:2</td>
<td>400</td>
<td>400</td>
<td>400</td>
<td>0</td>
<td>3.2</td>
<td>SDR</td>
<td>128</td>
<td>6.0</td>
</tr>
<tr>
<th style="text-align:left">Voodoo5 5000</th>
<td>Never Released</td>
<td>VSA-100 x2</td>
<td>250</td>
<td>AGP 4x, PCI</td>
<td>32</td>
<td>166</td>
<td>166</td>
<td>2:0:2:2 x2</td>
<td>664</td>
<td>664</td>
<td>664</td>
<td>0</td>
<td>2.656</td>
<td>SDR</td>
<td>128</td>
<td>6.0</td>
</tr>
<tr>
<th style="text-align:left">Voodoo5 5500</th>
<td>June 22, 2000</td>
<td>VSA-100 x2</td>
<td>250</td>
<td>AGP 4x, PCI</td>
<td>64</td>
<td>166</td>
<td>166</td>
<td>2:0:2:2 x2</td>
<td>664</td>
<td>664</td>
<td>664</td>
<td>0</td>
<td>2.656</td>
<td>SDR</td>
<td>128</td>
<td>6.0</td>
</tr>
<tr>
<th style="text-align:left">Voodoo5 6000</th>
<td>Never Released</td>
<td>VSA-100 x4</td>
<td>250</td>
<td>AGP 4x, PCI</td>
<td>128</td>
<td>166</td>
<td>166</td>
<td>2:0:2:2 x4</td>
<td>1328</td>
<td>1328</td>
<td>1328</td>
<td>0</td>
<td>5.312</td>
<td>SDR</td>
<td>256</td>
<td>6.0</td>
</tr>
<tr>
<th style="text-align:left">Spectre 1000</th>
<td>Never Released</td>
<td>Rampage</td>
<td>180</td>
<td>AGP 4x</td>
<td>64</td>
<td>200</td>
<td>400</td>
<td>4:0:4:4</td>
<td>800</td>
<td>800</td>
<td>800</td>
<td>0</td>
<td>6.4</td>
<td>DDR</td>
<td>128</td>
<td>?</td>
</tr>
<tr>
<th style="text-align:left">Spectre 2000</th>
<td>Never Released</td>
<td>Rampage + Sage</td>
<td>180</td>
<td>AGP 4x</td>
<td>64</td>
<td>200</td>
<td>400</td>
<td>4:0:4:4</td>
<td>800</td>
<td>800</td>
<td>800</td>
<td>0</td>
<td>6.4</td>
<td>DDR</td>
<td>128</td>
<td>?</td>
</tr>
<tr>
<th style="text-align:left">Spectre 3000</th>
<td>Never Released</td>
<td>Rampage x2 + Sage</td>
<td>180</td>
<td>AGP 4x</td>
<td>128</td>
<td>200</td>
<td>400</td>
<td>4:0:4:4 x2</td>
<td>800</td>
<td>800</td>
<td>800</td>
<td>0</td>
<td>12.8</td>
<td>DDR</td>
<td>256</td>
<td>?</td>
</tr>
</table>
