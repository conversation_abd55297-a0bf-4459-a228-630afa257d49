<table class="wikitable">
<tr>
<th>Device</th>
<th>Type</th>
<th>IOPS</th>
<th>Interface</th>
<th>Notes</th>
</tr>
<tr>
<td>Simple <a class="mw-redirect" href="//en.wikipedia.org/wiki/Single-level_cell" title="Single-level cell">SLC</a> <a href="//en.wikipedia.org/wiki/Solid-state_drive" title="Solid-state drive">SSD</a></td>
<td><a href="//en.wikipedia.org/wiki/Solid-state_drive" title="Solid-state drive">SSD</a></td>
<td>~400 IOPS<sup class="noprint Inline-Template Template-Fact" style="white-space:nowrap;">[<i><a href="//en.wikipedia.org/wiki/Wikipedia:Citation_needed" title="Wikipedia:Citation needed"><span title="This claim needs references to reliable sources. (February 2010)">citation needed</span></a></i>]</sup></td>
<td>SATA 3 Gbit/s</td>
<td></td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Intel#Solid-state_drives_.28SSD.29" title="Intel">Intel X25-M G2</a> (<a href="//en.wikipedia.org/wiki/Multi-level_cell" title="Multi-level cell">MLC</a>)</td>
<td>SSD</td>
<td>~8,600 IOPS<sup class="reference" id="cite_ref-11"><a href="#cite_note-11"><span>[</span>11<span>]</span></a></sup></td>
<td>SATA 3 Gbit/s</td>
<td>Intel's data sheet<sup class="reference" id="cite_ref-12"><a href="#cite_note-12"><span>[</span>12<span>]</span></a></sup> claims 6,600/8,600 IOPS (80 GB/160 GB version) and 35,000 IOPS for random 4 KB writes and reads, respectively.</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Intel#Solid-state_drives_.28SSD.29" title="Intel">Intel X25-E</a> (SLC)</td>
<td>SSD</td>
<td>~5,000 IOPS<sup class="reference" id="cite_ref-13"><a href="#cite_note-13"><span>[</span>13<span>]</span></a></sup></td>
<td>SATA 3 Gbit/s</td>
<td>Intel's data sheet<sup class="reference" id="cite_ref-14"><a href="#cite_note-14"><span>[</span>14<span>]</span></a></sup> claims 3,300 IOPS and 35,000 IOPS for writes and reads, respectively. 5,000 IOPS are measured for a mix. Intel X25-E G1 has around 3 times higher IOPS compared to the Intel X25-M G2.<sup class="reference" id="cite_ref-15"><a href="#cite_note-15"><span>[</span>15<span>]</span></a></sup></td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/G.Skill" title="G.Skill">G.Skill</a> Phoenix Pro</td>
<td>SSD</td>
<td>~20,000 IOPS<sup class="reference" id="cite_ref-tweakpc_16-0"><a href="#cite_note-tweakpc-16"><span>[</span>16<span>]</span></a></sup></td>
<td>SATA 3 Gbit/s</td>
<td><a href="//en.wikipedia.org/wiki/SandForce" title="SandForce">SandForce</a>-1200 based SSD drives with enhanced firmware, states up to 50,000 IOPS, but benchmarking shows for this particular drive ~25,000 IOPS for random read and ~15,000 IOPS for random write.<sup class="reference" id="cite_ref-tweakpc_16-1"><a href="#cite_note-tweakpc-16"><span>[</span>16<span>]</span></a></sup></td>
</tr>
<tr>
<td><a class="mw-redirect" href="//en.wikipedia.org/wiki/OCZ_Technology" title="OCZ Technology">OCZ</a> Vertex 3</td>
<td>SSD</td>
<td>Up to 60,000 IOPS<sup class="reference" id="cite_ref-17"><a href="#cite_note-17"><span>[</span>17<span>]</span></a></sup></td>
<td>SATA 6 Gbit/s</td>
<td>Random Write 4 KB (Aligned)</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Corsair_Memory" title="Corsair Memory">Corsair</a> Force Series GT</td>
<td>SSD</td>
<td>Up to 85,000 IOPS<sup class="reference" id="cite_ref-18"><a href="#cite_note-18"><span>[</span>18<span>]</span></a></sup></td>
<td>SATA 6 Gbit/s</td>
<td>240 GB Drive, 555 MB/s sequential read &amp; 525 MB/s sequential write, Random Write 4 KB Test (Aligned)</td>
</tr>
<tr>
<td><a class="mw-redirect" href="//en.wikipedia.org/wiki/OCZ_Technology" title="OCZ Technology">OCZ</a> Vertex 4</td>
<td>SSD</td>
<td>Up to 120,000 IOPS<sup class="reference" id="cite_ref-19"><a href="#cite_note-19"><span>[</span>19<span>]</span></a></sup></td>
<td>SATA 6 Gbit/s</td>
<td>256 GB Drive, 560 MB/s sequential read &amp; 510 MB/s sequential write, Random Read 4 KB Test 90K IOPS, Random Write 4 KB Test 85K IOPS</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Texas_Memory_Systems" title="Texas Memory Systems">Texas Memory Systems</a> RamSan-20</td>
<td>SSD</td>
<td>120,000+ Random Read/Write IOPS<sup class="reference" id="cite_ref-20"><a href="#cite_note-20"><span>[</span>20<span>]</span></a></sup></td>
<td><a class="mw-redirect" href="//en.wikipedia.org/wiki/PCIe" title="PCIe">PCIe</a></td>
<td>Includes RAM cache</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Fusion-io" title="Fusion-io">Fusion-io</a> ioDrive</td>
<td>SSD</td>
<td>140,000 Read IOPS, 135,000 Write IOPS<sup class="reference" id="cite_ref-21"><a href="#cite_note-21"><span>[</span>21<span>]</span></a></sup></td>
<td>PCIe</td>
<td></td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Virident_Systems" title="Virident Systems">Virident Systems</a> tachIOn</td>
<td>SSD</td>
<td>320,000 sustained READ IOPS using 4KB blocks and 200,000 sustained WRITE IOPS using 4KB blocks<sup class="reference" id="cite_ref-22"><a href="#cite_note-22"><span>[</span>22<span>]</span></a></sup></td>
<td>PCIe</td>
<td></td>
</tr>
<tr>
<td>OCZ RevoDrive 3 X2</td>
<td>SSD</td>
<td>200,000 Random Write 4K IOPS<sup class="reference" id="cite_ref-23"><a href="#cite_note-23"><span>[</span>23<span>]</span></a></sup></td>
<td>PCIe</td>
<td></td>
</tr>
<tr>
<td>Fusion-io ioDrive Duo</td>
<td>SSD</td>
<td>250,000+ IOPS<sup class="reference" id="cite_ref-24"><a href="#cite_note-24"><span>[</span>24<span>]</span></a></sup></td>
<td>PCIe</td>
<td></td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Violin_Memory" title="Violin Memory">Violin Memory</a> Violin 3200</td>
<td>SSD</td>
<td>250,000+ Random Read/Write IOPS<sup class="reference" id="cite_ref-25"><a href="#cite_note-25"><span>[</span>25<span>]</span></a></sup></td>
<td>PCIe /FC/Infiniband/iSCSI</td>
<td>Flash Memory Array</td>
</tr>
<tr>
<td>WHIPTAIL, <i>ACCELA</i></td>
<td>SSD</td>
<td>250,000/200,000+ Write/Read IOPS<sup class="reference" id="cite_ref-26"><a href="#cite_note-26"><span>[</span>26<span>]</span></a></sup></td>
<td>Fibre Channel, iSCSI, Infiniband/SRP, NFS, CIFS</td>
<td>Flash Based Storage Array</td>
</tr>
<tr>
<td><a class="new" href="//en.wikipedia.org/w/index.php?title=DDRdrive&amp;action=edit&amp;redlink=1" title="DDRdrive (page does not exist)">DDRdrive</a> X1,</td>
<td>SSD</td>
<td>300,000+ (512B Random Read IOPS) and 200,000+ (512B Random Write IOPS)<sup class="reference" id="cite_ref-27"><a href="#cite_note-27"><span>[</span>27<span>]</span></a></sup><sup class="reference" id="cite_ref-28"><a href="#cite_note-28"><span>[</span>28<span>]</span></a></sup><sup class="reference" id="cite_ref-29"><a href="#cite_note-29"><span>[</span>29<span>]</span></a></sup><sup class="reference" id="cite_ref-30"><a href="#cite_note-30"><span>[</span>30<span>]</span></a></sup></td>
<td>PCIe</td>
<td></td>
</tr>
<tr>
<td>SolidFire <i>SF3010/SF6010</i></td>
<td>SSD</td>
<td>250,000 4KB Read/Write IOPS<sup class="reference" id="cite_ref-31"><a href="#cite_note-31"><span>[</span>31<span>]</span></a></sup></td>
<td>iSCSI</td>
<td>Flash Based Storage Array (5RU)</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Texas_Memory_Systems" title="Texas Memory Systems">Texas Memory Systems</a> RamSan-720 Appliance</td>
<td>SSD</td>
<td>500,000 Optimal Read, 250,000 Optimal Write 4KB IOPS<sup class="reference" id="cite_ref-32"><a href="#cite_note-32"><span>[</span>32<span>]</span></a></sup></td>
<td>FC / InfiniBand</td>
<td></td>
</tr>
<tr>
<td>OCZ Single SuperScale Z-Drive R4 PCI-Express SSD</td>
<td>SSD</td>
<td>Up to 500,000 IOPS<sup class="reference" id="cite_ref-OCZ_Z-Drive_33-0"><a href="#cite_note-OCZ_Z-Drive-33"><span>[</span>33<span>]</span></a></sup></td>
<td>PCIe</td>
<td></td>
</tr>
<tr>
<td>WHIPTAIL, <i>INVICTA</i></td>
<td>SSD</td>
<td>650,000/550,000+ Read/Write IOPS<sup class="reference" id="cite_ref-34"><a href="#cite_note-34"><span>[</span>34<span>]</span></a></sup></td>
<td>Fibre Channel, iSCSI, Infiniband/SRP, NFS</td>
<td>Flash Based Storage Array</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Violin_Memory" title="Violin Memory">Violin Memory</a> Violin 6000</td>
<td>3RU Flash Memory Array</td>
<td>1,000,000+ Random Read/Write IOPS<sup class="reference" id="cite_ref-35"><a href="#cite_note-35"><span>[</span>35<span>]</span></a></sup></td>
<td>/FC/Infiniband/10Gb(iSCSI)/ PCIe</td>
<td></td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Texas_Memory_Systems" title="Texas Memory Systems">Texas Memory Systems</a> RamSan-630 Appliance</td>
<td>SSD</td>
<td>1,000,000+ 4KB Random Read/Write IOPS<sup class="reference" id="cite_ref-36"><a href="#cite_note-36"><span>[</span>36<span>]</span></a></sup></td>
<td>FC / InfiniBand</td>
<td></td>
</tr>
<tr>
<td>Fusion-io ioDrive Octal (single PCI Express card)</td>
<td>SSD</td>
<td>1,180,000+ Random Read/Write IOPS<sup class="reference" id="cite_ref-37"><a href="#cite_note-37"><span>[</span>37<span>]</span></a></sup></td>
<td>PCIe</td>
<td></td>
</tr>
<tr>
<td>OCZ 2x SuperScale Z-Drive R4 PCI-Express SSD</td>
<td>SSD</td>
<td>Up to 1,200,000 IOPS<sup class="reference" id="cite_ref-OCZ_Z-Drive_33-1"><a href="#cite_note-OCZ_Z-Drive-33"><span>[</span>33<span>]</span></a></sup></td>
<td>PCIe</td>
<td></td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Texas_Memory_Systems" title="Texas Memory Systems">Texas Memory Systems</a> RamSan-70</td>
<td>SSD</td>
<td>1,200,000 Random Read/Write IOPS<sup class="reference" id="cite_ref-38"><a href="#cite_note-38"><span>[</span>38<span>]</span></a></sup></td>
<td>PCIe</td>
<td>Includes RAM cache</td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/Kaminario" title="Kaminario">Kaminario</a> K2</td>
<td>Flash/DRAM/Hybrid SSD</td>
<td>Up to 1,200,000 IOPS SPC-1 IOPS with the K2-D (<a class="mw-redirect" href="//en.wikipedia.org/wiki/DRAM" title="DRAM">DRAM</a>)<sup class="reference" id="cite_ref-The_Register_39-0"><a href="#cite_note-The_Register-39"><span>[</span>39<span>]</span></a></sup><sup class="reference" id="cite_ref-Storage_Performance_Council_40-0"><a href="#cite_note-Storage_Performance_Council-40"><span>[</span>40<span>]</span></a></sup></td>
<td><a href="//en.wikipedia.org/wiki/Fibre_Channel" title="Fibre Channel">FC</a></td>
<td></td>
</tr>
<tr>
<td><a href="//en.wikipedia.org/wiki/NetApp" title="NetApp">NetApp</a> FAS6240 cluster</td>
<td>Flash/Disk</td>
<td>1,261,145 SPECsfs2008 nfsv3 IOPs using disks with virtual storage tiering.<sup class="reference" id="cite_ref-41"><a href="#cite_note-41"><span>[</span>41<span>]</span></a></sup></td>
<td>NFS, CIFS, FC, FCoE, iSCSI</td>
<td>SPECsfs2008 is the latest version of the Standard Performance Evaluation Corporation benchmark suite measuring file server throughput and response time, providing a standardized method for comparing performance across different vendor platforms. <a class="external free" href="http://www.spec.org/sfs2008/" rel="nofollow">http://www.spec.org/sfs2008/</a></td>
</tr>
<tr>
<td>Fusion-io ioDrive2</td>
<td>SSD</td>
<td>Up to 9,608,000 IOPS<sup class="reference" id="cite_ref-42"><a href="#cite_note-42"><span>[</span>42<span>]</span></a></sup></td>
<td>PCIe</td>
<td></td>
</tr>
</table>
