<table class="wikitable" style="margin-left:auto; margin-right:auto;">
<caption>Defining characteristics of some early digital computers of the 1940s <small style="font-size:85%;">(In the history of computing hardware)</small></caption>
<tr>
<th>Name</th>
<th>First operational</th>
<th>Numeral system</th>
<th>Computing mechanism</th>
<th><a href="//en.wikipedia.org/wiki/Computer_program" title="Computer program">Programming</a></th>
<th><a href="//en.wikipedia.org/wiki/Turing_completeness" title="Turing completeness">Turing complete</a></th>
</tr>
<tr>
<td class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/Konrad_Zuse" title="Konrad Zuse">Zuse</a> <a href="//en.wikipedia.org/wiki/Z3_(computer)" title="Z3 (computer)">Z3</a> <small style="font-size:85%;">(Germany)</small></td>
<td align="right">May 1941</td>
<td><a class="mw-redirect" href="//en.wikipedia.org/wiki/Binary_numeral_system" title="Binary numeral system">Binary</a> <a href="//en.wikipedia.org/wiki/Floating_point" title="Floating point">floating point</a></td>
<td><a href="//en.wikipedia.org/wiki/Electromechanics" title="Electromechanics">Electro-mechanical</a></td>
<td>Program-controlled by punched 35 mm <a href="//en.wikipedia.org/wiki/Film_stock" title="Film stock">film stock</a> (but no conditional branch)</td>
<td>In theory <small style="font-size:85%;">(<a href="//en.wikipedia.org/wiki/Z3_(computer)#Relation_to_the_concept_of_a_universal_Turing_machine" title="Z3 (computer)">1998</a>)</small></td>
</tr>
<tr>
<td class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a class="mw-redirect" href="//en.wikipedia.org/wiki/Atanasoff%E2%80%93Berry_Computer" title="Atanasoff–Berry Computer">Atanasoff–Berry Computer</a> <small style="font-size:85%;">(US)</small></td>
<td align="right">1942</td>
<td>Binary</td>
<td><a href="//en.wikipedia.org/wiki/Electronics" title="Electronics">Electronic</a></td>
<td>Not programmable—single purpose</td>
<td>No</td>
</tr>
<tr>
<td class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/Colossus_computer" title="Colossus computer">Colossus</a> Mark 1 <small style="font-size:85%;">(UK)</small></td>
<td align="right">February 1944</td>
<td>Binary</td>
<td>Electronic</td>
<td>Program-controlled by patch cables and switches</td>
<td>No</td>
</tr>
<tr>
<td class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/Harvard_Mark_I" title="Harvard Mark I">Harvard Mark I – IBM ASCC</a> <small style="font-size:85%;">(US)</small></td>
<td align="right">May 1944</td>
<td><a href="//en.wikipedia.org/wiki/Decimal" title="Decimal">Decimal</a></td>
<td>Electro-mechanical</td>
<td>Program-controlled by 24-channel <a href="//en.wikipedia.org/wiki/Punched_tape" title="Punched tape">punched paper tape</a> (but no conditional branch)</td>
<td>Debatable</td>
</tr>
<tr>
<td class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;">Colossus Mark 2 <small style="font-size:85%;">(UK)</small></td>
<td align="right">June 1944</td>
<td>Binary</td>
<td>Electronic</td>
<td>Program-controlled by patch cables and switches</td>
<td>In theory <small style="font-size:85%;">(2011)</small></td>
</tr>
<tr>
<td class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;">Zuse <a href="//en.wikipedia.org/wiki/Z4_(computer)" title="Z4 (computer)">Z4</a> <small style="font-size:85%;">(Germany)</small></td>
<td align="right">March 1945</td>
<td>Binary floating point</td>
<td>Electro-mechanical</td>
<td>Program-controlled by punched 35 mm film stock</td>
<td>Yes</td>
</tr>
<tr>
<td class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/ENIAC" title="ENIAC">ENIAC</a> <small style="font-size:85%;">(US)</small></td>
<td align="right">July 1946</td>
<td>Decimal</td>
<td>Electronic</td>
<td>Program-controlled by patch cables and switches</td>
<td>Yes</td>
</tr>
<tr>
<td class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/Manchester_Small-Scale_Experimental_Machine" title="Manchester Small-Scale Experimental Machine">Manchester Small-Scale Experimental Machine</a> (Baby) <small style="font-size:85%;">(UK)</small></td>
<td align="right">June 1948</td>
<td>Binary</td>
<td>Electronic</td>
<td><a href="//en.wikipedia.org/wiki/Stored-program_computer" title="Stored-program computer">Stored-program</a> in <a href="//en.wikipedia.org/wiki/Williams_tube" title="Williams tube">Williams cathode ray tube memory</a></td>
<td>Yes</td>
</tr>
<tr>
<td class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/ENIAC" title="ENIAC">Modified ENIAC</a> <small style="font-size:85%;">(US)</small></td>
<td align="right">September 1948</td>
<td>Decimal</td>
<td>Electronic</td>
<td>Read-only stored programming mechanism using the Function Tables as program <a href="//en.wikipedia.org/wiki/Read-only_memory" title="Read-only memory">ROM</a></td>
<td>Yes</td>
</tr>
<tr>
<td class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/Manchester_Mark_1" title="Manchester Mark 1">Manchester Mark 1</a> <small style="font-size:85%;">(UK)</small></td>
<td align="right">April 1949</td>
<td>Binary</td>
<td>Electronic</td>
<td>Stored-program in Williams cathode ray tube memory and <a href="//en.wikipedia.org/wiki/Drum_memory" title="Drum memory">magnetic drum</a> memory</td>
<td>Yes</td>
</tr>
<tr>
<td class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a class="mw-redirect" href="//en.wikipedia.org/wiki/EDSAC" title="EDSAC">EDSAC</a> <small style="font-size:85%;">(UK)</small></td>
<td align="right">May 1949</td>
<td>Binary</td>
<td>Electronic</td>
<td>Stored-program in mercury <a href="//en.wikipedia.org/wiki/Delay_line_memory" title="Delay line memory">delay line memory</a></td>
<td>Yes</td>
</tr>
<tr>
<td class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/CSIRAC" title="CSIRAC">CSIRAC</a> <small style="font-size:85%;">(Australia)</small></td>
<td align="right">November 1949</td>
<td>Binary</td>
<td>Electronic</td>
<td>Stored-program in mercury delay line memory</td>
<td>Yes</td>
</tr>
</table>
