"Devanagari","ISO 15919","UNRSGN","IAST","Comment"
"ए /  े","ē","e","e","To distinguish between long and short 'e' in Dravidian languages, 'e' now represents ऎ /  ॆ (short). Note that the use of ē is considered optional in ISO 15919, and using e for ए (long) is acceptable for languages that do not distinguish long and short e."
"ओ /  ो","ō","o","o","To distinguish between long and short 'o' in Dravidian languages, 'o' now represents ऒ /  ॊ (short). Note that the use of ō is considered optional in ISO 15919, and using o for ओ (long) is acceptable for languages that do not distinguish long and short o."
"ऋ /  ृ","r̥","ṛ","ṛ","In ISO 15919, ṛ is used to represent ड़."
"ॠ /  ॄ","r̥̄","ṝ","ṝ","For consistency with r̥"
"ऌ /  ॢ","l̥","l̤","ḷ","In ISO 15919, ḷ is used to represent ळ."
"ॡ /  ॣ","l̥̄","l̤̄","ḹ","For consistency with l̥"
"◌ं","ṁ","ṁ","ṃ","ISO 15919 has two options about anusvāra. (1) In the simplified nasalization option, an anusvāra is always transliterated as ṁ. (2) In the strict nasalization option, anusvāra before a class consonant is transliterated as the class nasal—ṅ before k, kh, g, gh, ṅ; ñ before c, ch, j, jh, ñ; ṇ before ṭ, ṭh, ḍ, ḍh, ṇ; n before t, th, d, dh, n; m before p, ph, b, bh, m. ṃ is sometimes used to specifically represent Gurmukhi Tippi  ੰ."
"◌ं","ṅ ñ ṇ n m","ṁ","ṃ","ISO 15919 has two options about anusvāra. (1) In the simplified nasalization option, an anusvāra is always transliterated as ṁ. (2) In the strict nasalization option, anusvāra before a class consonant is transliterated as the class nasal—ṅ before k, kh, g, gh, ṅ; ñ before c, ch, j, jh, ñ; ṇ before ṭ, ṭh, ḍ, ḍh, ṇ; n before t, th, d, dh, n; m before p, ph, b, bh, m. ṃ is sometimes used to specifically represent Gurmukhi Tippi  ੰ."
"◌ँ","m̐","","m̐","Vowel nasalization is transliterated as a tilde above the transliterated vowel (over the second vowel in the case of a digraph such as aĩ, aũ), except in Sanskrit."
