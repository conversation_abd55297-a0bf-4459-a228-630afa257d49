<table class="wikitable sortable" width="100%">
<tr>
<th data-sort-type="number" width="14%">ISO/IEC Standard</th>
<th width="29%">Title</th>
<th width="6%">Status</th>
<th width="49%">Description</th>
<th data-sort-type="number" width="2%">WG<sup class="reference" id="cite_ref-programme_11-0"><a href="#cite_note-programme-11"><span>[</span>11<span>]</span></a></sup></th>
</tr>
<tr>
<td data-sort-value="19759"><a href="//en.wikipedia.org/wiki/Software_Engineering_Body_of_Knowledge" title="Software Engineering Body of Knowledge">ISO/IEC TR 19759</a></td>
<td>Software Engineering – Guide to the Software Engineering Body of Knowledge (SWEBOK)</td>
<td>Published (2005)</td>
<td>Identifies and describes the subset of body of knowledge of software engineering that is generally accepted<sup class="reference" id="cite_ref-12"><a href="#cite_note-12"><span>[</span>12<span>]</span></a></sup></td>
<td data-sort-value="20">20</td>
</tr>
<tr>
<td data-sort-value="15288"><a href="//en.wikipedia.org/wiki/ISO/IEC_15288" title="ISO/IEC 15288">ISO/IEC 15288</a></td>
<td>Systems and software engineering – System life cycle processes</td>
<td>Published (2008)</td>
<td>Establishes a common framework for describing the life cycle of systems created by humans and defines a set of processes and associated terminology<sup class="reference" id="cite_ref-13"><a href="#cite_note-13"><span>[</span>13<span>]</span></a></sup></td>
<td data-sort-value="07">7</td>
</tr>
<tr>
<td data-sort-value="12207"><a href="//en.wikipedia.org/wiki/ISO/IEC_12207" title="ISO/IEC 12207">ISO/IEC 12207</a></td>
<td>Systems and software engineering – Software life cycle processes</td>
<td>Published (2008)</td>
<td>Establishes a common framework for software life cycle processes with well-defined terminology<sup class="reference" id="cite_ref-14"><a href="#cite_note-14"><span>[</span>14<span>]</span></a></sup></td>
<td data-sort-value="07">7</td>
</tr>
<tr>
<td data-sort-value="20000"><a href="//en.wikipedia.org/wiki/ISO/IEC_20000" title="ISO/IEC 20000">ISO/IEC 20000-1</a></td>
<td>Information technology – Service management – Part 1: Service management system requirements</td>
<td>Published (2011)</td>
<td>Specifies requirements for the service provider to plan, establish, implement, operate, monitor, review, maintain, and improve a service management system (SMS)<sup class="reference" id="cite_ref-15"><a href="#cite_note-15"><span>[</span>15<span>]</span></a></sup></td>
<td data-sort-value="25">25</td>
</tr>
<tr>
<td data-sort-value="15504"><a href="//en.wikipedia.org/wiki/ISO/IEC_15504" title="ISO/IEC 15504">ISO/IEC 15504</a>-1</td>
<td>Information technology – Process assessment – Part 1: Concepts and vocabulary</td>
<td>Published (2004)</td>
<td>Provides overall information on the concepts of process assessment and its use in the two contexts of process improvement and process capability determination<sup class="reference" id="cite_ref-16"><a href="#cite_note-16"><span>[</span>16<span>]</span></a></sup></td>
<td data-sort-value="10">10</td>
</tr>
<tr>
<td data-sort-value="42010"><a href="//en.wikipedia.org/wiki/ISO/IEC_42010" title="ISO/IEC 42010">ISO/IEC/IEEE 42010</a></td>
<td>Systems and software engineering – Architecture description</td>
<td>Published (2011)</td>
<td>Addresses the creation, analysis, and sustainment of architectures of systems through the use of architecture descriptions<sup class="reference" id="cite_ref-17"><a href="#cite_note-17"><span>[</span>17<span>]</span></a></sup></td>
<td data-sort-value="42">42</td>
</tr>
<tr>
<td data-sort-value="29110"><a href="//en.wikipedia.org/wiki/ISO_29110" title="ISO 29110">ISO/IEC TR 29110</a>-1</td>
<td>Software engineering – Lifecycle profiles for Very Small Entities (VSEs) – Part 1: Overview</td>
<td>Published (2011)</td>
<td>Introduces the characteristics and requirements of a VSE and clarifies the rationale for VSE-specific profiles, documents, standards, and guides<sup class="reference" id="cite_ref-18"><a href="#cite_note-18"><span>[</span>18<span>]</span></a></sup></td>
<td data-sort-value="24">24</td>
</tr>
<tr>
<td data-sort-value="9126"><a href="//en.wikipedia.org/wiki/ISO/IEC_9126" title="ISO/IEC 9126">ISO/IEC TR 9126</a>-2</td>
<td>Software engineering – Product quality – Part 2: External metrics</td>
<td>Published (2003)</td>
<td>Provides external metrics for measuring attributes of six external quality characteristics defined in ISO/IEC 9126-1<sup class="reference" id="cite_ref-19"><a href="#cite_note-19"><span>[</span>19<span>]</span></a></sup></td>
<td></td>
</tr>
<tr>
<td data-sort-value="10746"><a href="//en.wikipedia.org/wiki/RM-ODP" title="RM-ODP">ISO/IEC 10746-1</a></td>
<td>Information technology – Open Distributed Processing – Reference model: Overview</td>
<td>Published (1998)</td>
<td>Provides:<sup class="reference" id="cite_ref-20"><a href="#cite_note-20"><span>[</span>20<span>]</span></a></sup>
<ul>
<li>An introduction and motivation for ODP</li>
<li>An overview of the Reference Model of Open Distributed Processing (RM-ODP) and an explanation of its key concepts</li>
<li>Gives guidance on the application of RM-ODP</li>
</ul>
</td>
<td>19</td>
</tr>
<tr>
<td data-sort-value="19770"><a href="//en.wikipedia.org/wiki/ISO/IEC_19770" title="ISO/IEC 19770">ISO/IEC 19770</a>-1</td>
<td>Information technology – Software asset management – Part 1: Processes and tiered assessment of conformance</td>
<td>Published (2012)</td>
<td>Establishes a baseline for an integrated set of processes for Software Assessment Management (SAM), divided into tiers to allow for incremental implementation, assessment, and recognition<sup class="reference" id="cite_ref-21"><a href="#cite_note-21"><span>[</span>21<span>]</span></a></sup></td>
<td data-sort-value="21">21</td>
</tr>
<tr>
<td data-sort-value="26511">ISO/IEC/IEEE 26511</td>
<td>Systems and software engineering — Requirements for managers of user documentation</td>
<td>Published (2011)</td>
<td>Specifies procedures for managing user documentation throughout the software life cycle.<sup class="reference" id="cite_ref-22"><a href="#cite_note-22"><span>[</span>22<span>]</span></a></sup></td>
<td data-sort-value="2">2</td>
</tr>
<tr>
<td data-sort-value="26512">ISO/IEC/IEEE 26512</td>
<td>Systems and software engineering -- Requirements for acquirers and suppliers of user documentation</td>
<td>Published (2011)</td>
<td>Defines the documentation process from the acquirer's standpoint and the supplier's standpoint.<sup class="reference" id="cite_ref-23"><a href="#cite_note-23"><span>[</span>23<span>]</span></a></sup></td>
<td data-sort-value="2">2</td>
</tr>
<tr>
<td data-sort-value="26513">ISO/IEC/IEEE 26513</td>
<td>Systems and software engineering — Requirements for testers and reviewers of user documentation</td>
<td>Published (2009)</td>
<td>Defines the process in which user documentation products are tested.<sup class="reference" id="cite_ref-24"><a href="#cite_note-24"><span>[</span>24<span>]</span></a></sup></td>
<td data-sort-value="2">2</td>
</tr>
<tr>
<td data-sort-value="26514">ISO/IEC/IEEE 26514</td>
<td>Systems and software engineering — Requirements for designers and developers of user documentation</td>
<td>Published (2008)</td>
<td>Specifies the structure, content, and format for user documentation, and provides informative guidance for user documentation style.<sup class="reference" id="cite_ref-25"><a href="#cite_note-25"><span>[</span>25<span>]</span></a></sup></td>
<td data-sort-value="2">2</td>
</tr>
<tr>
<td data-sort-value="26515">ISO/IEC/IEEE 26515</td>
<td>Systems and software engineering — Developing user documentation in an agile environment</td>
<td>Published (2011)</td>
<td>Specifies the way in which user documentation can be developed in agile development projects.<sup class="reference" id="cite_ref-26"><a href="#cite_note-26"><span>[</span>26<span>]</span></a></sup></td>
<td data-sort-value="2">2</td>
</tr>
</table>
