"ISO/IEC Standard","Title","Status","Description","WG"
"ISO/IEC TR 19759","Software Engineering – Guide to the Software Engineering Body of Knowledge (SWEBOK)","Published (2005)","Identifies and describes the subset of body of knowledge of software engineering that is generally accepted","20"
"ISO/IEC 15288","Systems and software engineering – System life cycle processes","Published (2008)","Establishes a common framework for describing the life cycle of systems created by humans and defines a set of processes and associated terminology","7"
"ISO/IEC 12207","Systems and software engineering – Software life cycle processes","Published (2008)","Establishes a common framework for software life cycle processes with well-defined terminology","7"
"ISO/IEC 20000-1","Information technology – Service management – Part 1: Service management system requirements","Published (2011)","Specifies requirements for the service provider to plan, establish, implement, operate, monitor, review, maintain, and improve a service management system (SMS)","25"
"ISO/IEC 15504-1","Information technology – Process assessment – Part 1: Concepts and vocabulary","Published (2004)","Provides overall information on the concepts of process assessment and its use in the two contexts of process improvement and process capability determination","10"
"ISO/IEC/IEEE 42010","Systems and software engineering – Architecture description","Published (2011)","Addresses the creation, analysis, and sustainment of architectures of systems through the use of architecture descriptions","42"
"ISO/IEC TR 29110-1","Software engineering – Lifecycle profiles for Very Small Entities (VSEs) – Part 1: Overview","Published (2011)","Introduces the characteristics and requirements of a VSE and clarifies the rationale for VSE-specific profiles, documents, standards, and guides","24"
"ISO/IEC TR 9126-2","Software engineering – Product quality – Part 2: External metrics","Published (2003)","Provides external metrics for measuring attributes of six external quality characteristics defined in ISO/IEC 9126-1",""
"ISO/IEC 10746-1","Information technology – Open Distributed Processing – Reference model: Overview","Published (1998)","Provides:

An introduction and motivation for ODP
An overview of the Reference Model of Open Distributed Processing (RM-ODP) and an explanation of its key concepts
Gives guidance on the application of RM-ODP","19"
"ISO/IEC 19770-1","Information technology – Software asset management – Part 1: Processes and tiered assessment of conformance","Published (2012)","Establishes a baseline for an integrated set of processes for Software Assessment Management (SAM), divided into tiers to allow for incremental implementation, assessment, and recognition","21"
"ISO/IEC/IEEE 26511","Systems and software engineering — Requirements for managers of user documentation","Published (2011)","Specifies procedures for managing user documentation throughout the software life cycle.","2"
"ISO/IEC/IEEE 26512","Systems and software engineering -- Requirements for acquirers and suppliers of user documentation","Published (2011)","Defines the documentation process from the acquirer's standpoint and the supplier's standpoint.","2"
"ISO/IEC/IEEE 26513","Systems and software engineering — Requirements for testers and reviewers of user documentation","Published (2009)","Defines the process in which user documentation products are tested.","2"
"ISO/IEC/IEEE 26514","Systems and software engineering — Requirements for designers and developers of user documentation","Published (2008)","Specifies the structure, content, and format for user documentation, and provides informative guidance for user documentation style.","2"
"ISO/IEC/IEEE 26515","Systems and software engineering — Developing user documentation in an agile environment","Published (2011)","Specifies the way in which user documentation can be developed in agile development projects.","2"
